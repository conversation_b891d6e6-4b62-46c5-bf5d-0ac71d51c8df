<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <div class="alert alert-info mb-4"
      style="border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;">
      <div class="d-flex align-items-center">
        <i class="fas fa-info-circle text-primary me-3" style="font-size: 1.2rem;"></i>
        <div>
          <p class="mb-0 text-muted" style="font-size: 0.9rem;">
            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。
          </p>
        </div>
      </div>
    </div>

    <!-- 搜尋條件區域 -->
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="templateName" class="label col-3">模板名稱</label>
          <nb-form-field class="col-9">
            <input type="text" id="templateName" nbInput class="w-full" placeholder="搜尋模板名稱..."
              [(ngModel)]="searchKeyword" (keyup.enter)="onSearch()">
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="status" class="label col-3">狀態</label>
          <nb-form-field class="col-9">
            <nb-select id="status" placeholder="選擇狀態..." [(ngModel)]="searchStatus" (selectedChange)="onSearch()">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="1">啟用</nb-option>
              <nb-option [value]="0">停用</nb-option>
            </nb-select>
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="templateType" class="label col-3">模板類型</label>
          <nb-form-field class="col-9">
            <nb-select id="templateType" placeholder="選擇模板類型..." [(ngModel)]="searchTemplateType"
              (selectedChange)="onSearch()">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="1">空間模板</nb-option>
              <nb-option [value]="2">項目模板</nb-option>
            </nb-select>
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->
      </div>

      <!-- 查詢和重置按鈕 -->
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-2 mb-3">
          <button class="btn btn-outline-secondary btn-sm me-2" (click)="onReset()">
            <i class="fas fa-undo me-1"></i>重置
          </button>
          <button class="btn btn-secondary btn-sm" (click)="onSearch()">
            <i class="fas fa-search me-1"></i>查詢
          </button>
        </div>
      </div>

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-3">
          <button class="btn btn-info mx-1 btn-sm" *ngIf="isCreate" (click)="openCreateModal(createModal)">
            <i class="fas fa-plus me-1"></i>新增模板
          </button>
        </div>
      </div>
    </div>

    <!-- 模板列表表格 -->
    <div class="table-responsive mt-4">
      <table class="table" style="min-width: 800px;">
        <thead>
          <tr>
            <th scope="col" style="width: 120px;">模板類型</th>
            <th scope="col" style="width: 200px;">模板名稱</th>
            <th scope="col" style="width: 100px;">狀態</th>
            <th scope="col" style="width: 180px;">建立時間</th>
            <th scope="col" style="width: 120px;">建立者</th>
            <th scope="col" style="width: 140px;">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let template of templateList">
            <td>
              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}
            </td>
            <td>{{ template.CTemplateName }}</td>
            <td>
              <span class="badge" [ngClass]="template.CStatus === 1 ? 'badge-success' : 'badge-secondary'">
                {{ template.CStatus === 1 ? '啟用' : '停用' }}
              </span>
            </td>
            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>
            <td>{{ template.CCreator || '-' }}</td>
            <td class="table-actions">
              <button class="btn btn-outline-info btn-sm me-1"
                (click)="viewTemplateDetail(template, templateDetailModal)">
                <i class="fas fa-eye"></i>查看
              </button>
              <button *ngIf="isUpdate" class="btn btn-outline-warning btn-sm me-1"
                (click)="openEditModal(editModal, template)">
                <i class="fas fa-edit"></i>編輯
              </button>
              <button *ngIf="isDelete" class="btn btn-outline-danger btn-sm" (click)="deleteTemplate(template)">
                <i class="fas fa-trash"></i>刪除
              </button>
            </td>
          </tr>
          <tr *ngIf="templateList.length === 0">
            <td colspan="5" class="text-muted py-4">
              <i class="fas fa-info-circle me-2"></i>目前沒有任何模板
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(Page)]="pageIndex" [PageSize]="pageSize" [CollectionSize]="totalRecords"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<!-- 新增模板模態框 -->
<ng-template #createModal let-ref="dialogRef">
  <nb-card style="width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-plus-circle me-2 text-success"></i>新增模板
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <div class="row">
        <!-- 模板名稱 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="templateName" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                模板名稱
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="templateName" class="form-control" nbInput placeholder="請輸入模板名稱"
                  [(ngModel)]="templateDetail.CTemplateName" name="templateName" (keydown.control.enter)="onSubmit(ref)"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 模板類型頁簽 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label class="required-field mb-0" style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                模板類型
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-tabset class="template-type-tabs">
                  <nb-tab tabTitle="空間模板" [active]="templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate"
                    (click)="templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate">
                    <!-- 空間選擇區域 -->
                    <div class="mt-3">
                      <!-- 搜尋區域 -->
                      <div class="row mb-3">
                        <div class="col-md-5">
                          <input type="text" class="form-control form-control-sm" placeholder="搜尋項目名稱..."
                            [(ngModel)]="spaceSearchKeyword" (keyup.enter)="onSpaceSearch()"
                            style="height: 32px; border-radius: 4px;" />
                        </div>
                        <div class="col-md-5">
                          <input type="text" class="form-control form-control-sm" placeholder="搜尋所屬區域..."
                            [(ngModel)]="spaceSearchLocation" (keyup.enter)="onSpaceSearch()"
                            style="height: 32px; border-radius: 4px;" />
                        </div>
                        <div class="col-md-2">
                          <button class="btn btn-sm btn-outline-secondary me-1" (click)="onSpaceReset()">
                            <i class="fas fa-undo"></i>
                          </button>
                          <button class="btn btn-sm btn-secondary" (click)="onSpaceSearch()">
                            <i class="fas fa-search"></i>
                          </button>
                        </div>
                      </div>

                      <!-- 可選空間列表 -->
                      <div class="border rounded p-3" style="background-color: #f8f9fa;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                          <div class="d-flex align-items-center">
                            <input type="checkbox" id="selectAll" [checked]="allSpacesSelected"
                              (change)="toggleAllSpaces()" class="me-2">
                            <label for="selectAll" class="mb-0 font-weight-bold">全選當頁空間</label>
                          </div>
                          <small class="text-muted">
                            共 {{ spaceTotalRecords }} 筆，第 {{ spacePageIndex }} / {{ Math.ceil(spaceTotalRecords /
                            spacePageSize) }} 頁
                          </small>
                        </div>

                        <!-- 空間項目網格 -->
                        <div class="space-grid">
                          <div class="space-item" *ngFor="let space of availableSpaces"
                            [class.selected]="space.selected" (click)="toggleSpaceSelection(space)">
                            <div class="space-card">
                              <div class="space-name">{{ space.CPart }}</div>
                              <div class="space-location">{{ space.CLocation || '-' }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- 空間列表為空時的提示 -->
                        <div *ngIf="availableSpaces.length === 0" class="text-center text-muted py-4">
                          <i class="fas fa-info-circle me-2"></i>沒有符合條件的空間
                        </div>

                        <!-- 分頁 -->
                        <div class="w-100 d-flex flex-column align-items-center mt-4"
                          *ngIf="spaceTotalRecords > spacePageSize">
                          <div class="mb-2 text-secondary" style="font-size: 0.95rem;">
                            共 <span class="fw-bold text-primary">{{ spaceTotalRecords }}</span> 筆資料
                          </div>
                          <ngx-pagination [(Page)]="spacePageIndex" [PageSize]="spacePageSize"
                            [CollectionSize]="spaceTotalRecords" (PageChange)="spacePageChanged($event)">
                          </ngx-pagination>
                        </div>
                      </div>

                      <!-- 已選空間 -->
                      <div class="mt-3" *ngIf="selectedSpacesForTemplate.length > 0">
                        <label class="mb-2 font-weight-bold">已選擇的空間 ({{ selectedSpacesForTemplate.length }})</label>
                        <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                          <span class="badge badge-primary me-1 mb-1" *ngFor="let space of selectedSpacesForTemplate">
                            {{ space.CPart }}
                            <button type="button" class="btn-close btn-close-white ms-1"
                              (click)="removeSelectedSpace(space)" style="font-size: 0.7rem;"></button>
                          </span>
                        </div>
                      </div>
                    </div>
                  </nb-tab>
                  <nb-tab tabTitle="項目模板" [active]="templateDetail.CTemplateType === EnumTemplateType.ItemTemplate"
                    (click)="templateDetail.CTemplateType = EnumTemplateType.ItemTemplate">
                    <span slot="tabTitle">項目模板</span>

                    <!-- 項目模板專用欄位 -->
                    <div class="mt-3">
                      <div class="row">
                        <!-- 單價 -->
                        <div class="col-md-6">
                          <div class="form-group mb-4">
                            <div class="d-flex align-items-start">
                              <label for="unitPrice" class="mb-0"
                                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                                單價
                              </label>
                              <div class="flex-grow-1 ml-3">
                                <input type="number" id="unitPrice" class="form-control" nbInput placeholder="請輸入單價"
                                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 單位 -->
                        <div class="col-md-6">
                          <div class="form-group mb-4">
                            <div class="d-flex align-items-start">
                              <label for="unit" class="mb-0"
                                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                                單位
                              </label>
                              <div class="flex-grow-1 ml-3">
                                <input type="text" id="unit" class="form-control" nbInput placeholder="請輸入單位"
                                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </nb-tab>
                </nb-tabset>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板狀態 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="templateStatus" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                狀態
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-form-field class="w-full">
                  <nb-select id="templateStatus" [(ngModel)]="templateDetail.CStatus" name="templateStatus"
                    placeholder="選擇狀態" style="height: 42px;">
                    <nb-option [value]="1">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-2"></i>啟用
                      </span>
                    </nb-option>
                    <nb-option [value]="0">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-times-circle text-danger me-2"></i>停用
                      </span>
                    </nb-option>
                  </nb-select>
                </nb-form-field>
              </div>
            </div>
          </div>
        </div>


      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-outline-secondary me-3 px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>取消
      </button>
      <button class="btn btn-primary px-4" (click)="onSubmit(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);">
        <i class="fas fa-check me-1"></i>確認
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 編輯模板模態框 -->
<ng-template #editModal let-ref="dialogRef">
  <nb-card style="width: 550px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-edit me-2 text-warning"></i>編輯模板
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <div class="row">
        <!-- 模板名稱 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="editTemplateName" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                模板名稱
              </label>
              <div class="flex-grow-1 ml-3">
                <input type="text" id="editTemplateName" class="form-control" nbInput placeholder="請輸入模板名稱"
                  [(ngModel)]="templateDetail.CTemplateName" name="editTemplateName"
                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
              </div>
            </div>
          </div>
        </div>

        <!-- 模板類型頁簽 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label class="required-field mb-0" style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                模板類型
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-tabset class="template-type-tabs">
                  <nb-tab tabTitle="空間模板" [active]="templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate"
                    (click)="templateDetail.CTemplateType = EnumTemplateType.SpaceTemplate">
                    <span slot="tabTitle">空間模板</span>

                    <!-- 編輯模式下空間模板說明 -->
                    <div class="mt-3">
                      <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        編輯模式下，空間配置請在模板詳情中進行管理。
                      </div>
                    </div>
                  </nb-tab>
                  <nb-tab tabTitle="項目模板" [active]="templateDetail.CTemplateType === EnumTemplateType.ItemTemplate"
                    (click)="templateDetail.CTemplateType = EnumTemplateType.ItemTemplate">
                    <span slot="tabTitle">項目模板</span>

                    <!-- 項目模板專用欄位 -->
                    <div class="mt-3">
                      <div class="row">
                        <!-- 單價 -->
                        <div class="col-md-6">
                          <div class="form-group mb-4">
                            <div class="d-flex align-items-start">
                              <label for="editUnitPrice" class="mb-0"
                                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                                單價
                              </label>
                              <div class="flex-grow-1 ml-3">
                                <input type="number" id="editUnitPrice" class="form-control" nbInput placeholder="請輸入單價"
                                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 單位 -->
                        <div class="col-md-6">
                          <div class="form-group mb-4">
                            <div class="d-flex align-items-start">
                              <label for="editUnit" class="mb-0"
                                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                                單位
                              </label>
                              <div class="flex-grow-1 ml-3">
                                <input type="text" id="editUnit" class="form-control" nbInput placeholder="請輸入單位"
                                  style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </nb-tab>
                </nb-tabset>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板狀態 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="d-flex align-items-start">
              <label for="editTemplateStatus" class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">
                狀態
              </label>
              <div class="flex-grow-1 ml-3">
                <nb-form-field class="w-full">
                  <nb-select id="editTemplateStatus" [(ngModel)]="templateDetail.CStatus" name="editTemplateStatus"
                    placeholder="選擇狀態" style="height: 42px;">
                    <nb-option [value]="1">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-2"></i>啟用
                      </span>
                    </nb-option>
                    <nb-option [value]="0">
                      <span class="d-flex align-items-center">
                        <i class="fas fa-times-circle text-danger me-2"></i>停用
                      </span>
                    </nb-option>
                  </nb-select>
                </nb-form-field>
              </div>
            </div>
          </div>
        </div>


      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-outline-secondary me-3 px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>取消
      </button>
      <button class="btn btn-primary px-4" (click)="onSubmit(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);">
        <i class="fas fa-save me-1"></i>確認
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<!-- 查看模板明細模態框 -->
<ng-template #templateDetailModal let-ref="dialogRef">
  <nb-card style="width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-eye me-2 text-info"></i>模板明細
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <!-- 模板基本資訊 -->
      <div class="card mb-4" style="border: 1px solid #e4e7ea; border-radius: 8px;">
        <div class="card-header" style="background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;">
          <h6 class="mb-0 text-dark font-weight-bold">
            <i class="fas fa-info-circle me-2 text-primary"></i>基本資訊
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-tag me-2 text-primary"></i>模板名稱
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-toggle-on me-2 text-success"></i>狀態
                </label>
                <p class="mb-0">
                  <span class="badge"
                    [ngClass]="selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'">
                    <i [class]="selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'"
                      class="me-1"></i>
                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-calendar-plus me-2 text-info"></i>建立時間
                </label>
                <p class="mb-0">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-user-plus me-2 text-warning"></i>建立者
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CCreator || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-calendar-edit me-2 text-info"></i>更新時間
                </label>
                <p class="mb-0">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-user-edit me-2 text-warning"></i>更新者
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 包含的空間列表 -->
      <div class="card" style="border: 1px solid #e4e7ea; border-radius: 8px;">
        <div class="card-header d-flex justify-content-between align-items-center"
          style="background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;">
          <h6 class="mb-0 text-dark font-weight-bold">
            <i class="fas fa-home me-2 text-success"></i>包含的空間
          </h6>
          <span class="badge badge-info">共 {{ templateDetailSpaces.length }} 個空間</span>
        </div>
        <div class="card-body">
          <!-- Loading 狀態 -->
          <div *ngIf="isLoadingTemplateDetail" class="text-center py-4">
            <i class="fas fa-spinner fa-spin me-2 text-primary" style="font-size: 1.2rem;"></i>
            <span class="text-muted">載入中...</span>
          </div>

          <!-- 空間列表 -->
          <div *ngIf="!isLoadingTemplateDetail">
            <div class="table-responsive" *ngIf="templateDetailSpaces.length > 0">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th scope="col" class="col-1">#</th>
                    <th scope="col" class="col-7">項目名稱</th>
                    <th scope="col" class="col-4">所屬區域</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let space of templateDetailSpaces; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>
                      <i class="fas fa-home me-2 text-muted"></i>{{ space.CPart }}
                    </td>
                    <td>
                      <i class="fas fa-map-marker-alt me-2 text-muted"></i>{{ space.CLocation || '-' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 沒有空間時的提示 -->
            <div *ngIf="templateDetailSpaces.length === 0" class="text-center text-muted py-4">
              <i class="fas fa-info-circle me-2"></i>此模板尚未包含任何空間
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-secondary px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>關閉
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>